# SVG 转换为 GeoJSON 工具需求文档

## 概述

该工具旨在将 SVG 文件转换为 GeoJSON 格式，并在一个填充整个窗口的地图上显示 SVG 图层。用户可以通过侧边栏面板上传 SVG 文件并设置相关参数。

## 功能需求

### 1. 地图显示

- 使用 MapTiler 制作一个填充整个窗口的地图。
- 地图应支持缩放和平移操作。

### 2. 侧边栏面板

- 右侧有一个侧边栏面板，包含以下功能：
- **SVG 文件导入**
  - 用户可以上传 SVG 文件。
- **参数设置**
  - **中心点 (center)**: 输入默认定位的中心点坐标。
  - **缩放级别 (zoom)**: 输入默认缩放级别。
  - **卫星地图开关（https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}）**

### 3. 图层显示

- 上传的 SVG 图层应显示在地图上，且图层的中心点与设置的默认定位中心点重合。
- 用户可以设置 SVG 的整体缩放比例：
- 通过滑块进行调整。
- 通过输入框精确调整。

### 4. 旋转调整

- 用户可以通过滑块和输入框调整 SVG 的旋转角度。

### 5. 生成 GeoJSON

- 用户在调整好图层显示后，点击“生成”按钮，将 SVG 转换为对应位置的 GeoJSON 文件。
- 用户可以选择查看源 SVG 效果或 GeoJSON 的加载效果。

### 6. 实时调整

- 用户可以随时调整参数并重新生成 GeoJSON 文件。

## 用户界面设计

- **地图区域**: 占据整个窗口。
- **侧边栏**: 右侧面板，包含上传按钮、中心点输入框、缩放级别输入框、缩放比例滑块、旋转角度滑块和生成按钮。

## 技术要求

- 使用 HTML, CSS 和 JavaScript 构建前端界面。
- 使用 MapTiler API 进行地图显示。
- 使用适当的库进行 SVG 转换为 GeoJSON 的处理。

## 总结

该工具将为用户提供一个直观的界面，方便地将 SVG 文件转换为 GeoJSON 格式，并实时查看效果。
