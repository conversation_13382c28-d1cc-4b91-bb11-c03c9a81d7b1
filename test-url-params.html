<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG to GeoJSON - URL参数测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .example h3 {
            margin-top: 0;
            color: #495057;
        }
        .url {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .param {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 8px;
            margin: 5px 0;
        }
        .param strong {
            color: #0c5460;
        }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 6px;
            margin-top: 10px;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>SVG to GeoJSON - URL参数功能测试</h1>
    
    <p>现在应用支持通过URL参数自定义地图源、中心点和缩放级别。以下是一些测试示例：</p>

    <div class="example">
        <h3>示例1: 自定义地图源 + 中心点 + 缩放</h3>
        <div class="param">
            <strong>mapUrl:</strong> http://173.100.1.152:8085/{z}/{x}/{y}.png
        </div>
        <div class="param">
            <strong>center:</strong> 116.404,39.915 (北京)
        </div>
        <div class="param">
            <strong>zoom:</strong> 15
        </div>
        <div class="url">
            dist/index.html?mapUrl=http%3A%2F%2F173.100.1.152%3A8085%2F%7Bz%7D%2F%7Bx%7D%2F%7By%7D.png&center=116.404,39.915&zoom=15
        </div>
        <a href="dist/index.html?mapUrl=http%3A%2F%2F173.100.1.152%3A8085%2F%7Bz%7D%2F%7Bx%7D%2F%7By%7D.png&center=116.404,39.915&zoom=15" 
           class="test-link" target="_blank">测试此链接</a>
    </div>

    <div class="example">
        <h3>示例2: 只设置中心点和缩放</h3>
        <div class="param">
            <strong>center:</strong> 121.474,31.233 (上海)
        </div>
        <div class="param">
            <strong>zoom:</strong> 12
        </div>
        <div class="url">
            dist/index.html?center=121.474,31.233&zoom=12
        </div>
        <a href="dist/index.html?center=121.474,31.233&zoom=12" 
           class="test-link" target="_blank">测试此链接</a>
    </div>

    <div class="example">
        <h3>示例3: 你提供的完整URL</h3>
        <div class="param">
            <strong>mapUrl:</strong> http://173.100.1.152:8085/{z}/{x}/{y}.png
        </div>
        <div class="param">
            <strong>center:</strong> 0,0 (赤道几内亚湾)
        </div>
        <div class="param">
            <strong>zoom:</strong> 15
        </div>
        <div class="url">
            dist/index.html?mapUrl=http%3A%2F%2F173.100.1.152%3A8085%2F%7Bz%7D%2F%7Bx%7D%2F%7By%7D.png&center=0,0&zoom=15
        </div>
        <a href="dist/index.html?mapUrl=http%3A%2F%2F173.100.1.152%3A8085%2F%7Bz%7D%2F%7Bx%7D%2F%7By%7D.png&center=0,0&zoom=15" 
           class="test-link" target="_blank">测试此链接</a>
    </div>

    <h2>参数说明</h2>
    <ul>
        <li><strong>mapUrl</strong>: 自定义地图瓦片服务URL，需要URL编码。支持{z}/{x}/{y}占位符</li>
        <li><strong>center</strong>: 地图中心点，格式为"经度,纬度"</li>
        <li><strong>zoom</strong>: 地图缩放级别，通常范围1-20</li>
    </ul>

    <h2>功能特性</h2>
    <ul>
        <li>✅ 自动解析URL参数并应用设置</li>
        <li>✅ 支持自定义地图瓦片服务</li>
        <li>✅ 自动设置地图中心点和缩放级别</li>
        <li>✅ 在侧边栏显示当前使用的地图源信息</li>
        <li>✅ 兼容默认的Mapbox地图样式</li>
    </ul>

    <p><em>注意：如果自定义地图服务不可用，地图可能显示空白。请确保地图服务URL正确且可访问。</em></p>
</body>
</html> 