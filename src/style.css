/* 导入 Mapbox GL 样式 */
@import url('https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css');

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100vh;
  overflow: hidden;
}

#app {
  height: 100vh;
  display: flex;
  position: relative;
}

/* 地图容器 */
#map {
  flex: 1;
  height: 100vh;
  width: calc(100vw - 320px);
}

/* 侧边栏 */
.sidebar {
  width: 320px;
  background: #ffffff;
  border-left: 1px solid #e0e0e0;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
}

.sidebar-content {
  padding: 20px;
}

.sidebar h2 {
  color: #2c3e50;
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  border-bottom: 2px solid #3498db;
  padding-bottom: 12px;
}

.sidebar h3 {
  color: #34495e;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 500;
}

/* 区块样式 */
.section {
  margin-bottom: 32px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* 表单组样式 */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #495057;
  font-weight: 500;
  font-size: 14px;
}

.form-group input[type="number"],
.form-group input[type="text"] {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input[type="number"]:focus,
.form-group input[type="text"]:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 转换方法选择器样式 */
.conversion-method-group {
  display: flex;
  gap: 16px;
  margin: 8px 0;
}

.radio-label {
  display: flex !important;
  align-items: center;
  gap: 6px;
  font-weight: normal !important;
  margin-bottom: 0 !important;
  cursor: pointer;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: all 0.3s;
}

.radio-label:hover {
  background-color: #f8f9fa;
  border-color: #3498db;
}

.radio-label input[type="radio"] {
  margin: 0;
  width: auto;
}

.radio-label input[type="radio"]:checked + span,
.radio-label:has(input[type="radio"]:checked) {
  background-color: #e3f2fd;
  border-color: #3498db;
  color: #1976d2;
}

.radio-label input[type="radio"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.radio-label:has(input[type="radio"]:disabled) {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 滑块样式 */
.slider {
  width: 100%;
  margin: 8px 0;
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: #ddd;
  outline: none;
  border-radius: 3px;
  transition: background 0.3s;
}

.slider:hover {
  background: #bbb;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #3498db;
  cursor: pointer;
  border-radius: 50%;
  transition: background 0.3s;
}

.slider::-webkit-slider-thumb:hover {
  background: #2980b9;
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #3498db;
  cursor: pointer;
  border-radius: 50%;
  border: none;
  transition: background 0.3s;
}

.slider::-moz-range-thumb:hover {
  background: #2980b9;
}

/* 文件输入样式 */
.file-input {
  display: none;
}

/* 按钮样式 */
.upload-btn,
.clear-btn,
.generate-btn,
.download-btn {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
}

.upload-btn {
  background: #6c757d;
  color: white;
}

.upload-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.clear-btn {
  background: #dc3545;
  color: white;
}

.clear-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.generate-btn {
  background: #28a745;
  color: white;
}

.generate-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.generate-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.download-btn {
  background: #17a2b8;
  color: white;
}

.download-btn:hover {
  background: #138496;
  transform: translateY(-1px);
}

/* 复选框样式 */
.form-group label input[type="checkbox"] {
  margin-right: 8px;
  width: auto;
  transform: scale(1.2);
}

.form-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* 状态消息 */
.status-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  z-index: 10000;
  max-width: 400px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideDown 0.3s ease-out;
}

.status-message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.status-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  #app {
    flex-direction: column;
  }
  
  #map {
    width: 100vw;
    height: 60vh;
  }
  
  .sidebar {
    width: 100vw;
    height: 40vh;
    border-left: none;
    border-top: 1px solid #e0e0e0;
  }
  
  .sidebar-content {
    padding: 16px;
  }
  
  .section {
    margin-bottom: 20px;
    padding: 16px;
  }
  
  .sidebar h2 {
    font-size: 20px;
    margin-bottom: 16px;
  }
  
  .sidebar h3 {
    font-size: 16px;
    margin-bottom: 12px;
  }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 数字输入框组合样式 */
.form-group .slider + input[type="number"] {
  margin-top: 8px;
  width: 80px;
  float: right;
}

/* 地图URL输入组合样式 */
.map-url-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 4px;
}

.map-url-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
  font-family: 'Courier New', monospace;
}

.map-url-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.apply-btn {
  padding: 10px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 60px;
}

.apply-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.apply-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.clear-map-btn {
  padding: 10px 16px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 60px;
}

.clear-map-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.map-source-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 4px;
}

.map-source-info small {
  color: #6c757d;
  word-break: break-all;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 搜索功能样式 */
.search-container {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.search-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.search-btn {
  padding: 10px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 80px;
}

.search-btn:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-1px);
}

.search-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.search-results {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-result-item {
  padding: 12px;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: #f8f9fa;
}

.result-name {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 4px;
}

.result-type {
  font-size: 12px;
  color: #6c757d;
}

/* 搜索结果滚动条样式 */
.search-results::-webkit-scrollbar {
  width: 4px;
}

.search-results::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.search-results::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.search-results::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 中心点保存功能样式 */
.center-point-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.save-center-btn {
  width: 100%;
  padding: 10px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.save-center-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.hint {
  color: #6c757d;
  font-size: 12px;
  text-align: center;
  font-style: italic;
}

.drag-hint {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 16px;
}

.drag-hint small {
  color: #1976d2;
  font-size: 12px;
  font-weight: 500;
}

/* 保存的中心点列表样式 */
.saved-centers {
  max-height: 300px;
  overflow-y: auto;
}

.saved-center-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
  transition: all 0.2s;
}

.saved-center-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #3498db;
}

.center-info {
  flex: 1;
  margin-right: 12px;
}

.center-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 4px;
}

.center-coords {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  color: #495057;
  margin-bottom: 2px;
}

.center-meta {
  font-size: 11px;
  color: #6c757d;
}

.center-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.jump-btn,
.delete-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.jump-btn {
  background: #17a2b8;
  color: white;
}

.jump-btn:hover {
  background: #138496;
  transform: scale(1.1);
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* 保存中心点列表滚动条样式 */
.saved-centers::-webkit-scrollbar {
  width: 4px;
}

.saved-centers::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.saved-centers::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.saved-centers::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* GeoJSON颜色控制样式 */
.color-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.color-picker {
  width: 50px;
  height: 40px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
  background: none;
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-picker::-webkit-color-swatch {
  border: none;
  border-radius: 3px;
}

.color-text {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  text-transform: uppercase;
}

.color-text:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 区块标题样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
}

.clear-geojson-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: #dc3545;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-geojson-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* 文件上传组样式 */
.file-upload-group {
  margin-bottom: 12px;
}

.upload-btn.secondary {
  background: #17a2b8;
  color: white;
}

.upload-btn.secondary:hover {
  background: #138496;
  transform: translateY(-1px);
}

/* 操作组样式 */
.action-group {
  margin-bottom: 16px;
}

.button-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.button-row button {
  flex: 1;
  margin-bottom: 0;
}

/* 复制按钮样式 */
.copy-btn {
  background: #6f42c1;
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.copy-btn:hover {
  background: #5a2d91;
  transform: translateY(-1px);
}

/* 提交按钮样式 */
.submit-btn {
  background: #007bff;
  color: white;
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
}

.submit-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

/* 适应视图按钮样式 */
.fit-btn {
  background: #fd7e14;
  color: white;
  width: 100%;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
}

.fit-btn:hover {
  background: #e8590c;
  transform: translateY(-1px);
}

/* 重置位置按钮样式 */
.reset-position-btn {
  background: #20c997;
  color: white;
  width: 100%;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.reset-position-btn:hover {
  background: #1aa179;
  transform: translateY(-1px);
}

/* 改进按钮图标间距 */
button[class*="btn"] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 响应式改进 */
@media (max-width: 768px) {
  .button-row {
    flex-direction: column;
    gap: 4px;
  }
  
  .button-row button {
    width: 100%;
  }
}
