<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG2GeoJSON URL参数示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .section h2 {
            margin-top: 0;
            color: #2c3e50;
        }
        .example-url {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
            word-break: break-all;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .params-table th,
        .params-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .params-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SVG2GeoJSON URL参数使用指南</h1>
        
        <div class="section">
            <h2>🌍 支持的URL参数</h2>
            <table class="params-table">
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>描述</th>
                        <th>示例值</th>
                        <th>必需</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>mapUrl</code></td>
                        <td>自定义地图瓦片URL模板</td>
                        <td>http://173.100.1.152:8085/{z}/{x}/{y}.png</td>
                        <td>否</td>
                    </tr>
                    <tr>
                        <td><code>center</code></td>
                        <td>地图中心点坐标 (经度,纬度)</td>
                        <td>116.404,39.915</td>
                        <td>否</td>
                    </tr>
                    <tr>
                        <td><code>zoom</code></td>
                        <td>地图缩放级别</td>
                        <td>15</td>
                        <td>否</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h2>📝 使用示例</h2>
            
            <h3>示例1: 使用自定义地图源</h3>
            <div class="example-url">
                index.html?mapUrl=http%3A%2F%2F173.100.1.152%3A8085%2F%7Bz%7D%2F%7Bx%7D%2F%7By%7D.png
            </div>
            
            <h3>示例2: 设置中心点和缩放级别</h3>
            <div class="example-url">
                index.html?center=116.404,39.915&zoom=15
            </div>
            
            <h3>示例3: 完整配置</h3>
            <div class="example-url">
                index.html?mapUrl=http%3A%2F%2F173.100.1.152%3A8085%2F%7Bz%7D%2F%7Bx%7D%2F%7By%7D.png&center=116.404,39.915&zoom=15
            </div>
        </div>
        
        <div class="section">
            <h2>🔧 URL编码说明</h2>
            <p>mapUrl参数需要进行URL编码。常见字符的编码对照：</p>
            <table class="params-table">
                <thead>
                    <tr>
                        <th>原字符</th>
                        <th>编码后</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>:</td><td>%3A</td></tr>
                    <tr><td>/</td><td>%2F</td></tr>
                    <tr><td>{</td><td>%7B</td></tr>
                    <tr><td>}</td><td>%7D</td></tr>
                    <tr><td>?</td><td>%3F</td></tr>
                    <tr><td>&</td><td>%26</td></tr>
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h2>🚀 快速测试链接</h2>
            <p>点击下面的链接测试不同的参数组合：</p>
            
            <a href="index.html" class="btn">默认配置</a>
            
            <a href="index.html?center=0,0&zoom=15" class="btn">设置中心点 (0,0)</a>
            
            <a href="index.html?center=116.404,39.915&zoom=12" class="btn">北京天安门</a>
            
            <a href="index.html?center=121.473,31.230&zoom=12" class="btn">上海外滩</a>
            
            <div class="note">
                <strong>注意：</strong> 自定义地图源链接需要确保服务器支持CORS跨域访问，否则可能无法正常加载。
            </div>
        </div>
        
        <div class="section">
            <h2>💡 功能说明</h2>
            <ul>
                <li><strong>mapUrl</strong>: 设置后会替换默认的Mapbox卫星图，支持标准的{z}/{x}/{y}瓦片格式</li>
                <li><strong>center</strong>: 设置地图的初始中心点坐标</li>
                <li><strong>zoom</strong>: 设置地图的初始缩放级别 (1-20)</li>
                <li>使用自定义地图源时，侧边栏的"卫星地图"选项会显示为"自定义地图"</li>
                <li>自定义地图源信息会在侧边栏的"地图设置"部分显示</li>
            </ul>
        </div>
    </div>
</body>
</html> 