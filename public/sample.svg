<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <!-- 示例矩形 -->
  <rect x="20" y="20" width="60" height="40" fill="#ff6b6b" stroke="#000" stroke-width="2"/>
  
  <!-- 示例圆形 -->
  <circle cx="150" cy="50" r="25" fill="#4ecdc4" stroke="#000" stroke-width="2"/>
  
  <!-- 示例路径 - 三角形 -->
  <path d="M100 80 L130 140 L70 140 Z" fill="#45b7d1" stroke="#000" stroke-width="2"/>
  
  <!-- 示例复杂路径 -->
  <path d="M20 160 Q50 120 80 160 T140 160" fill="none" stroke="#96ceb4" stroke-width="3"/>
  
  <!-- 示例多边形 -->
  <polygon points="160,120 180,100 200,120 200,160 160,160" fill="#feca57" stroke="#000" stroke-width="2"/>
</svg> 